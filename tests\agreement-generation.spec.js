import { test, expect } from '@playwright/test';

test.describe('Comprehensive Agreement Template Integration', () => {
  test('should verify comprehensive template is accessible and complete', async ({ page }) => {
    console.log('🧪 Starting Comprehensive Agreement Template Test...');

    // Test 1: Verify template is accessible in production
    console.log('📋 Test 1: Verifying template accessibility...');

    const templateResponse = await page.request.get('https://royalty.technology/templates/v2/comprehensive_contributor_agreement_template.md');
    expect(templateResponse.status()).toBe(200);

    const templateText = await templateResponse.text();
    expect(templateText.length).toBeGreaterThan(60000); // Should be around 64k characters

    // Verify key sections are present
    const keySections = [
      'CONTRIBUTOR AGREEMENT',
      'Recitals',
      '1. Definitions',
      '2. Treatment of Confidential Information',
      '3. Ownership of Work Product',
      'SCHEDULE A',
      'SCHEDULE B',
      'EXHIBIT I',
      'EXHIBIT II'
    ];

    keySections.forEach(section => {
      expect(templateText).toContain(section);
    });

    // Verify comprehensive sections (new in v2)
    const comprehensiveSections = [
      'Contribution Points',
      'Revenue Tranche',
      'Restrictive Covenants',
      'Non-solicitation',
      'Non-competition',
      'Indemnification',
      'Buy Out Provisions',
      'Sequel Rights'
    ];

    comprehensiveSections.forEach(section => {
      expect(templateText).toContain(section);
    });

    // Verify conditional logic
    const conditionalSections = [
      '{{#IF PROJECT_TYPE_GAME}}',
      '{{#IF PROJECT_TYPE_SOFTWARE}}',
      '{{#IF INCLUDE_ADVANCED_RESTRICTIVE_COVENANTS}}',
      '{{#IF INCLUDE_BUYOUT_PROVISIONS}}',
      '{{#IF INCLUDE_SEQUEL_RIGHTS}}'
    ];

    conditionalSections.forEach(conditional => {
      expect(templateText).toContain(conditional);
    });

    console.log('✅ Template accessibility and content verification passed');
    console.log(`📏 Template loaded: ${templateText.length} characters`);
  });

  test('should verify template loading works in browser context', async ({ page }) => {
    console.log('📋 Test 2: Testing template loading in browser context...');

    await page.goto('https://royalty.technology');
    await page.waitForLoadState('networkidle');

    const templateLoadTest = await page.evaluate(async () => {
      try {
        const response = await fetch('/templates/v2/comprehensive_contributor_agreement_template.md');
        if (!response.ok) {
          return { success: false, error: `HTTP ${response.status}` };
        }
        const text = await response.text();
        return {
          success: true,
          length: text.length,
          hasKey: text.includes('CONTRIBUTOR AGREEMENT') && text.includes('Revenue Tranche'),
          hasConditionals: text.includes('{{#IF PROJECT_TYPE_GAME}}') && text.includes('{{#IF INCLUDE_BUYOUT_PROVISIONS}}')
        };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    expect(templateLoadTest.success).toBe(true);
    expect(templateLoadTest.length).toBeGreaterThan(60000);
    expect(templateLoadTest.hasKey).toBe(true);
    expect(templateLoadTest.hasConditionals).toBe(true);

    console.log('✅ Template loading in browser context works correctly');
    console.log(`📏 Template loaded: ${templateLoadTest.length} characters`);
  });

  test('should verify agreement generation system accessibility', async ({ page }) => {
    console.log('📋 Test 3: Testing agreement generation system accessibility...');

    await page.goto('https://royalty.technology');
    await page.waitForLoadState('networkidle');

    // Take screenshot of initial state
    await page.screenshot({ path: 'test-results/01-initial-page.png', fullPage: true });

    // Check if we need to login
    const emailInput = page.locator('input[type="email"], input[name="email"], input[placeholder*="email" i]');
    const passwordInput = page.locator('input[type="password"], input[name="password"], input[placeholder*="password" i]');
    const loginButton = page.locator('button:has-text("Login"), button:has-text("Sign In"), input[type="submit"]');

    if (await emailInput.isVisible()) {
      console.log('🔐 Login required, authenticating...');
      await emailInput.fill('<EMAIL>');
      await passwordInput.fill('TestPassword123!');
      await loginButton.first().click();
      await page.waitForLoadState('networkidle');
      await page.screenshot({ path: 'test-results/02-after-login.png', fullPage: true });
    }

    // Try to navigate directly to project wizard
    console.log('🔄 Navigating to project wizard...');
    await page.goto('https://royalty.technology/project/wizard');
    await page.waitForLoadState('networkidle');
    await page.screenshot({ path: 'test-results/03-project-wizard.png', fullPage: true });

    // Look for project wizard elements
    const wizardElements = [
      'h1:has-text("Project")',
      'h2:has-text("Project")',
      'h1:has-text("Create")',
      'h2:has-text("Create")',
      '.project-wizard',
      '[data-testid="project-wizard"]'
    ];

    let wizardFound = false;
    for (const selector of wizardElements) {
      if (await page.locator(selector).isVisible()) {
        console.log(`✅ Found wizard element: ${selector}`);
        wizardFound = true;
        break;
      }
    }

    if (wizardFound) {
      console.log('✅ Project wizard is accessible');

      // Look for agreement-related elements
      const agreementElements = [
        'button:has-text("Agreement")',
        'a:has-text("Agreement")',
        'h3:has-text("Agreement")',
        'h4:has-text("Agreement")',
        '.agreement',
        '[data-testid="agreement"]'
      ];

      let agreementFound = false;
      for (const selector of agreementElements) {
        if (await page.locator(selector).isVisible()) {
          console.log(`✅ Found agreement element: ${selector}`);
          agreementFound = true;
          break;
        }
      }

      if (agreementFound) {
        console.log('✅ Agreement generation is accessible in wizard');
      } else {
        console.log('ℹ️  Agreement elements not immediately visible (may require wizard progression)');
      }
    } else {
      console.log('ℹ️  Project wizard not immediately accessible');
    }

    await page.screenshot({ path: 'test-results/04-final-state.png', fullPage: true });

    console.log('🎉 Agreement generation system accessibility test completed');
  });

  test('should verify NewAgreementGenerator works correctly in browser', async ({ page }) => {
    console.log('🚀 Test 4: Testing NewAgreementGenerator in browser context...');

    await page.goto('https://royalty.technology');
    await page.waitForLoadState('networkidle');

    // Test the NewAgreementGenerator directly in browser context
    const generatorTest = await page.evaluate(async () => {
      try {
        // Import the NewAgreementGenerator
        const { NewAgreementGenerator } = await import('/src/utils/agreement/newAgreementGenerator.js');
        const generator = new NewAgreementGenerator();

        // Test template loading
        const templateText = await generator.loadTemplate('standard');

        if (!templateText) {
          return { success: false, error: 'Template loading returned null/undefined' };
        }

        // Verify it's the comprehensive template
        const isComprehensive = templateText.includes('Revenue Tranche') &&
                               templateText.includes('Restrictive Covenants') &&
                               templateText.includes('Indemnification');

        // Test basic agreement generation
        const mockProject = {
          name: 'Test Project',
          description: 'Test Description',
          type: 'SOFTWARE'
        };

        const mockOptions = {
          contributors: [{ name: 'Test User', email: '<EMAIL>' }],
          currentUser: { name: 'Test User', email: '<EMAIL>' },
          royaltyModel: { model_type: 'UNIFIED_POOL', platform_fee: 5 },
          milestones: [],
          templateType: 'comprehensive'
        };

        const agreement = await generator.generateAgreement(templateText, mockProject, mockOptions);

        return {
          success: true,
          generatorLoaded: !!generator,
          templateLoaded: !!templateText,
          templateLength: templateText.length,
          isComprehensive: isComprehensive,
          agreementGenerated: !!agreement,
          agreementLength: agreement?.length || 0,
          agreementHasProjectName: agreement?.includes('Test Project') || false,
          agreementIsComprehensive: agreement?.includes('Revenue Tranche') || false
        };
      } catch (error) {
        return { success: false, error: error.message, stack: error.stack };
      }
    });

    console.log('Generator test result:', generatorTest);

    // Verify the test results
    expect(generatorTest.success).toBe(true);
    expect(generatorTest.generatorLoaded).toBe(true);
    expect(generatorTest.templateLoaded).toBe(true);
    expect(generatorTest.templateLength).toBeGreaterThan(60000);
    expect(generatorTest.isComprehensive).toBe(true);
    expect(generatorTest.agreementGenerated).toBe(true);
    expect(generatorTest.agreementLength).toBeGreaterThan(1000);
    expect(generatorTest.agreementHasProjectName).toBe(true);
    expect(generatorTest.agreementIsComprehensive).toBe(true);

    console.log('✅ NewAgreementGenerator works correctly in browser context');
    console.log(`📏 Template: ${generatorTest.templateLength} chars, Agreement: ${generatorTest.agreementLength} chars`);
  });
});


